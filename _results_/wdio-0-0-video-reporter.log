[2025-09-06T03:04:25.348Z] Add frame for command: /session/:sessionId/element/00000000-0000-01b9-ffff-ffff0000002b/clear => [clear]
[2025-09-06T03:04:25.872Z] Add frame for command: /session/:sessionId/element/00000000-0000-01b9-ffff-ffff0000002b/value => [value]
[2025-09-06T03:04:26.126Z] - Screenshot (frame: 0)
[2025-09-06T03:04:26.592Z] - Screenshot (frame: 1)
[2025-09-06T03:04:26.598Z] Add frame for command: /session/:sessionId/element/00000000-0000-01b9-ffff-ffff0000002e/clear => [clear]
[2025-09-06T03:04:27.125Z] Add frame for command: /session/:sessionId/element/00000000-0000-01b9-ffff-ffff0000002e/value => [value]
[2025-09-06T03:04:27.284Z] - Screenshot (frame: 2)
[2025-09-06T03:04:27.817Z] - Screenshot (frame: 3)
[2025-09-06T03:04:27.891Z] Add frame for command: /session/:sessionId/element/00000000-0000-01b9-ffff-ffff0000002f/click => [click]
[2025-09-06T03:04:28.172Z] - Screenshot (frame: 4)
[2025-09-06T03:04:28.181Z] ffmpeg command: "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/node_modules/@ffmpeg-installer/darwin-arm64/ffmpeg" -y -r 10 -i "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/.video-reporter-screenshots/User-accesses-login-page-0-0--browser--09-06-2025--10-04-24-449/%04d.png" -vcodec libvpx-vp9 -crf 32 -pix_fmt yuv420p -vf "scale=1200:trunc(ow/a/2)*2","setpts=1.0*PTS" "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/User-accesses-login-page-0-0--browser--09-06-2025--10-04-24-449.webm"
[2025-09-06T03:04:28.192Z] Screenshot not available (frame: 6). Error: invalid session id: WebDriverError: A session is either terminated or not started when running "screenshot" with method "GET"..
[2025-09-06T03:04:28.417Z] - Screenshot (frame: 5)
[2025-09-06T03:04:29.174Z] videoRenderTimeout triggered before ffmpeg had a chance to wrap up
[2025-09-06T03:04:29.174Z] Generated 1 videos, video report done!
[2025-09-06T03:04:29.183Z] Generated video: "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/User-accesses-login-page-0-0--browser--09-06-2025--10-04-24-449.webm" (1002ms)
