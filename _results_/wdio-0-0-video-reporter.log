[2025-09-06T03:12:13.123Z] Add frame for command: /session/:sessionId/element/00000000-0000-01fb-ffff-ffff0000002b/clear => [clear]
[2025-09-06T03:12:14.170Z] Add frame for command: /session/:sessionId/element/00000000-0000-01fb-ffff-ffff0000002b/value => [value]
[2025-09-06T03:12:14.356Z] - Screenshot (frame: 0)
[2025-09-06T03:12:14.837Z] - Screenshot (frame: 1)
[2025-09-06T03:12:14.840Z] Add frame for command: /session/:sessionId/element/00000000-0000-01fb-ffff-ffff0000002e/clear => [clear]
[2025-09-06T03:12:15.863Z] Add frame for command: /session/:sessionId/element/00000000-0000-01fb-ffff-ffff0000002e/value => [value]
[2025-09-06T03:12:16.016Z] - Screenshot (frame: 2)
[2025-09-06T03:12:16.520Z] - Screenshot (frame: 3)
[2025-09-06T03:12:16.557Z] Add frame for command: /session/:sessionId/element/00000000-0000-01fb-ffff-ffff0000002f/click => [click]
[2025-09-06T03:12:16.780Z] - Screenshot (frame: 4)
[2025-09-06T03:12:16.793Z] ffmpeg command: "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/node_modules/@ffmpeg-installer/darwin-arm64/ffmpeg" -y -r 10 -i "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/.video-reporter-screenshots/User-accesses-login-page-0-0--browser--09-06-2025--10-12-12-323/%04d.png" -vcodec libvpx-vp9 -crf 32 -pix_fmt yuv420p -vf "scale=1200:trunc(ow/a/2)*2","setpts=1.0*PTS" "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/User-accesses-login-page-0-0--browser--09-06-2025--10-12-12-323.webm"
[2025-09-06T03:12:16.977Z] Screenshot not available (frame: 6). Error: invalid session id: WebDriverError: A session is either terminated or not started when running "screenshot" with method "GET"..
[2025-09-06T03:12:16.978Z] Screenshot not available (frame: 5). Error: invalid session id: WebDriverError: A session is either terminated or not started when running "screenshot" with method "GET"..
[2025-09-06T03:12:17.247Z] videoRenderTimeout triggered before ffmpeg had a chance to wrap up
[2025-09-06T03:12:17.247Z] Generated 1 videos, video report done!
