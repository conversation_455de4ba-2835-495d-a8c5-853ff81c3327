[2025-09-06T01:33:40.987Z] Add frame for command: /session/:sessionId/element/00000000-0000-0153-ffff-ffff0000002b/clear => [clear]
[2025-09-06T01:33:42.041Z] Add frame for command: /session/:sessionId/element/00000000-0000-0153-ffff-ffff0000002b/value => [value]
[2025-09-06T01:33:42.241Z] - Screenshot (frame: 0)
[2025-09-06T01:33:42.704Z] - Screenshot (frame: 1)
[2025-09-06T01:33:42.707Z] Add frame for command: /session/:sessionId/element/00000000-0000-0153-ffff-ffff0000002e/clear => [clear]
[2025-09-06T01:33:43.737Z] Add frame for command: /session/:sessionId/element/00000000-0000-0153-ffff-ffff0000002e/value => [value]
[2025-09-06T01:33:43.892Z] - Screenshot (frame: 2)
[2025-09-06T01:33:44.397Z] - Screenshot (frame: 3)
[2025-09-06T01:33:44.503Z] Add frame for command: /session/:sessionId/element/00000000-0000-0153-ffff-ffff0000002f/click => [click]
[2025-09-06T01:33:44.674Z] - Screenshot (frame: 4)
[2025-09-06T01:33:44.679Z] ffmpeg command: "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/node_modules/@ffmpeg-installer/darwin-arm64/ffmpeg" -y -r 10 -i "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/.video-reporter-screenshots/User-accesses-login-page-0-0--browser--09-06-2025--08-33-40-047/%04d.png" -vcodec libvpx-vp9 -crf 32 -pix_fmt yuv420p -vf "scale=1200:trunc(ow/a/2)*2","setpts=1.0*PTS" "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/User-accesses-login-page-0-0--browser--09-06-2025--08-33-40-047.webm"
[2025-09-06T01:33:44.690Z] Screenshot not available (frame: 6). Error: invalid session id: WebDriverError: A session is either terminated or not started when running "screenshot" with method "GET"..
[2025-09-06T01:33:44.857Z] - Screenshot (frame: 5)
[2025-09-06T01:33:45.256Z] videoRenderTimeout triggered before ffmpeg had a chance to wrap up
[2025-09-06T01:33:45.256Z] Generated 1 videos, video report done!
