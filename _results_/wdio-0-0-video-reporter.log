[2025-09-06T03:07:17.731Z] Add frame for command: /session/:sessionId/element/00000000-0000-01cf-ffff-ffff0000002b/clear => [clear]
[2025-09-06T03:07:18.274Z] Add frame for command: /session/:sessionId/element/00000000-0000-01cf-ffff-ffff0000002b/value => [value]
[2025-09-06T03:07:18.475Z] - Screenshot (frame: 0)
[2025-09-06T03:07:18.960Z] - Screenshot (frame: 1)
[2025-09-06T03:07:18.967Z] Add frame for command: /session/:sessionId/element/00000000-0000-01cf-ffff-ffff0000002e/clear => [clear]
[2025-09-06T03:07:19.523Z] Add frame for command: /session/:sessionId/element/00000000-0000-01cf-ffff-ffff0000002e/value => [value]
[2025-09-06T03:07:19.666Z] - Screenshot (frame: 2)
[2025-09-06T03:07:20.168Z] - Screenshot (frame: 3)
[2025-09-06T03:07:20.203Z] Add frame for command: /session/:sessionId/element/00000000-0000-01cf-ffff-ffff0000002f/click => [click]
[2025-09-06T03:07:20.206Z] ffmpeg command: "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/node_modules/@ffmpeg-installer/darwin-arm64/ffmpeg" -y -r 10 -i "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/.video-reporter-screenshots/User-accesses-login-page-0-0--browser--09-06-2025--10-07-16-926/%04d.png" -vcodec libvpx-vp9 -crf 32 -pix_fmt yuv420p -vf "scale=1200:trunc(ow/a/2)*2","setpts=1.0*PTS" "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/User-accesses-login-page-0-0--browser--09-06-2025--10-07-16-926.webm"
[2025-09-06T03:07:20.484Z] - Screenshot (frame: 4)
[2025-09-06T03:07:20.535Z] Screenshot not available (frame: 5). Error: invalid session id: WebDriverError: A session is either terminated or not started when running "screenshot" with method "GET"..
[2025-09-06T03:07:20.536Z] Screenshot not available (frame: 6). Error: invalid session id: WebDriverError: A session is either terminated or not started when running "screenshot" with method "GET"..
[2025-09-06T03:07:21.038Z] videoRenderTimeout triggered before ffmpeg had a chance to wrap up
[2025-09-06T03:07:21.038Z] Generated 1 videos, video report done!
