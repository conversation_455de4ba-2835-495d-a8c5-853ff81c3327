[2025-09-06T01:31:17.325Z] Add frame for command: /session/:sessionId/element/00000000-0000-013b-ffff-ffff0000002b/clear => [clear]
[2025-09-06T01:31:18.372Z] Add frame for command: /session/:sessionId/element/00000000-0000-013b-ffff-ffff0000002b/value => [value]
[2025-09-06T01:31:18.590Z] - Screenshot (frame: 0)
[2025-09-06T01:31:18.708Z] - Screenshot (frame: 1)
[2025-09-06T01:31:18.895Z] Add frame for command: /session/:sessionId/element/00000000-0000-013b-ffff-ffff0000002e/clear => [clear]
[2025-09-06T01:31:19.421Z] Add frame for command: /session/:sessionId/element/00000000-0000-013b-ffff-ffff0000002e/value => [value]
[2025-09-06T01:31:19.581Z] - Screenshot (frame: 2)
[2025-09-06T01:31:20.272Z] - Screenshot (frame: 3)
[2025-09-06T01:31:20.384Z] Add frame for command: /session/:sessionId/element/00000000-0000-013b-ffff-ffff0000002f/click => [click]
[2025-09-06T01:31:20.540Z] - Screenshot (frame: 4)
[2025-09-06T01:31:20.546Z] ffmpeg command: "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/node_modules/@ffmpeg-installer/darwin-arm64/ffmpeg" -y -r 10 -i "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/.video-reporter-screenshots/User-accesses-login-page-0-0--browser--09-06-2025--08-31-16-288/%04d.png" -vcodec libvpx-vp9 -crf 32 -pix_fmt yuv420p -vf "scale=1200:trunc(ow/a/2)*2","setpts=1.0*PTS" "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/User-accesses-login-page-0-0--browser--09-06-2025--08-31-16-288.webm"
[2025-09-06T01:31:20.724Z] - Screenshot (frame: 5)
[2025-09-06T01:31:21.026Z] - Screenshot (frame: 6)
[2025-09-06T01:31:21.294Z] Generated video: "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/User-accesses-login-page-0-0--browser--09-06-2025--08-31-16-288.webm" (748ms)
[2025-09-06T01:31:21.617Z] Generated 1 videos, video report done!
