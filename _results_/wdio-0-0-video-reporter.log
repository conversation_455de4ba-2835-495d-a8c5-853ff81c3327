[2025-09-06T03:18:27.264Z] Add frame for command: /session/:sessionId/element/00000000-0000-021c-ffff-ffff0000002b/clear => [clear]
[2025-09-06T03:18:28.323Z] Add frame for command: /session/:sessionId/element/00000000-0000-021c-ffff-ffff0000002b/value => [value]
[2025-09-06T03:18:28.565Z] - Screenshot (frame: 0)
[2025-09-06T03:18:28.965Z] - Screenshot (frame: 1)
[2025-09-06T03:18:28.969Z] Add frame for command: /session/:sessionId/element/00000000-0000-021c-ffff-ffff0000002e/clear => [clear]
[2025-09-06T03:18:29.511Z] Add frame for command: /session/:sessionId/element/00000000-0000-021c-ffff-ffff0000002e/value => [value]
[2025-09-06T03:18:29.665Z] - Screenshot (frame: 2)
[2025-09-06T03:18:30.170Z] - Screenshot (frame: 3)
[2025-09-06T03:18:30.296Z] Add frame for command: /session/:sessionId/element/00000000-0000-021c-ffff-ffff0000002f/click => [click]
[2025-09-06T03:18:30.464Z] - Screenshot (frame: 4)
[2025-09-06T03:18:30.475Z] ffmpeg command: "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/node_modules/@ffmpeg-installer/darwin-arm64/ffmpeg" -y -r 10 -i "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/.video-reporter-screenshots/User-accesses-login-page-0-0--browser--09-06-2025--10-18-26-503/%04d.png" -vcodec libvpx-vp9 -crf 32 -pix_fmt yuv420p -vf "scale=1200:trunc(ow/a/2)*2","setpts=1.0*PTS" "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/User-accesses-login-page-0-0--browser--09-06-2025--10-18-26-503.webm"
[2025-09-06T03:18:30.674Z] - Screenshot (frame: 5)
[2025-09-06T03:18:31.034Z] - Screenshot (frame: 6)
[2025-09-06T03:18:31.407Z] Generated video: "/Users/<USER>/Documents/github/OUTSIDE/appium_wdio_ts/_results_/User-accesses-login-page-0-0--browser--09-06-2025--10-18-26-503.webm" (932ms)
[2025-09-06T03:18:31.588Z] Generated 1 videos, video report done!
