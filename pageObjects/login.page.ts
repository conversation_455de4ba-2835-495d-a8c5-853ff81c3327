
class LoginPage {

  private getElementByDesc(desc: string) {
    return $(`//*[@content-desc="${desc}"]`);
  }

  // Use accessibility id (~) for locating mobile elements
  private getElementById(id: string) {
    return $(`~${id}`);
  }

  async doLogin(username: string = "standard_user", password: string = "secret_sauce") {
    await this.getElementById("test-Username").setValue(username);
    await this.getElementById("test-Password").setValue(password);
    await this.getElementById("test-LOGIN").click();
  }
}

export default new LoginPage();
