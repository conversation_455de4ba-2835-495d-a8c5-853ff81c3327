<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AntDesign.ttf</key>
		<data>
		TneGhDkoD7Q01Gl8e5EScUBsgfM=
		</data>
		<key><EMAIL></key>
		<data>
		Vn9fQC6unaAr3pkWsxkm4yEjLz8=
		</data>
		<key><EMAIL></key>
		<data>
		OGQaKHg5puh3lbk0I7YL6HilIcI=
		</data>
		<key><EMAIL></key>
		<data>
		0//hZOEFTymmiSE0mVCEdJT7zoE=
		</data>
		<key><EMAIL></key>
		<data>
		DmnHyAO1lKLoecP4nqkx5CDwo00=
		</data>
		<key><EMAIL></key>
		<data>
		zODOlcghFq0YVsH/r+R6OwuouhM=
		</data>
		<key><EMAIL></key>
		<data>
		k73LnAljYKnbGanUkF+Uh7mKEtM=
		</data>
		<key><EMAIL></key>
		<data>
		k73LnAljYKnbGanUkF+Uh7mKEtM=
		</data>
		<key><EMAIL></key>
		<data>
		IYs1eJJIvE3QyKp5ZOYijyN8JLQ=
		</data>
		<key>Assets.car</key>
		<data>
		XZgFFu7FpLx625lKCyyBVFE9dvI=
		</data>
		<key>Base.lproj/LaunchScreen.nib</key>
		<data>
		I4rS2bLRdmCHzIxaCTnwfnV/re0=
		</data>
		<key>Entypo.ttf</key>
		<data>
		R4cKZ91Qhxdl95nKqX2ochnuR8U=
		</data>
		<key>EvilIcons.ttf</key>
		<data>
		kdN36jz0dJCyVsLtCBcEp9q9rgw=
		</data>
		<key>Feather.ttf</key>
		<data>
		Um4D6mimHEpnpwsB17NF6rNJdn0=
		</data>
		<key>FontAwesome.ttf</key>
		<data>
		E7HqtlqYPHpzvHmXxHnWaUP3xss=
		</data>
		<key>FontAwesome5_Brands.ttf</key>
		<data>
		Zshu39E+Y+Dpgc6FMzXCi8LSDOw=
		</data>
		<key>FontAwesome5_Regular.ttf</key>
		<data>
		APVp99isJJekOyXcTTsyITvOoxs=
		</data>
		<key>FontAwesome5_Solid.ttf</key>
		<data>
		/483360QV5a9kk0NsNN6T/JSYYw=
		</data>
		<key>Fontisto.ttf</key>
		<data>
		wJCj7Jaj8bubYVwvPyBM4Nzc28M=
		</data>
		<key>Foundation.ttf</key>
		<data>
		SyvObHkkk6SlcWtv7C2+/olJLD8=
		</data>
		<key>Frameworks/libswiftCore.dylib</key>
		<data>
		rrAp64iPhUL40XQR7JlGMDIZaMA=
		</data>
		<key>Frameworks/libswiftCoreFoundation.dylib</key>
		<data>
		P4IT9OqWCWZ8FyecMxQHhM6h4G8=
		</data>
		<key>Frameworks/libswiftCoreGraphics.dylib</key>
		<data>
		JoDjsNQXE3z2hogOouALtjw8m/g=
		</data>
		<key>Frameworks/libswiftCoreImage.dylib</key>
		<data>
		dccNAYDUBxOzns3c+W4XRSmf1Ps=
		</data>
		<key>Frameworks/libswiftCoreLocation.dylib</key>
		<data>
		GMi1JxCYZe10XWexHCK2KQ5T2Lo=
		</data>
		<key>Frameworks/libswiftDarwin.dylib</key>
		<data>
		XJGVexKAsC220QzBQHwL6VS/whc=
		</data>
		<key>Frameworks/libswiftDispatch.dylib</key>
		<data>
		xYRhwsR1eeXatGDr8WNyVj93krw=
		</data>
		<key>Frameworks/libswiftFoundation.dylib</key>
		<data>
		WclOTQyDfPDOGEjZboBz+yfxGVQ=
		</data>
		<key>Frameworks/libswiftMetal.dylib</key>
		<data>
		q6qpoqLxtRrru4zloX0b0xHIJVw=
		</data>
		<key>Frameworks/libswiftObjectiveC.dylib</key>
		<data>
		W4v+YlroD8+v27M4zKa/3r9eqdA=
		</data>
		<key>Frameworks/libswiftQuartzCore.dylib</key>
		<data>
		OzOL7prTxnOJwhTRps72jdhagI4=
		</data>
		<key>Frameworks/libswiftUIKit.dylib</key>
		<data>
		G10/WJel9vPNJjANFC1WEM1Jno0=
		</data>
		<key>Frameworks/libswiftos.dylib</key>
		<data>
		oMpvzHAVfevcqm0jDkhUpqLzhXg=
		</data>
		<key>Info.plist</key>
		<data>
		H6KZS8tzDwAC9P/V3VmsA/0jkQE=
		</data>
		<key>Ionicons.ttf</key>
		<data>
		tSgfv6QxKavD5BLdk4z88Ld96Zk=
		</data>
		<key>MaterialCommunityIcons.ttf</key>
		<data>
		gKQx1tSYYdZVAJ6UkUCSgtJVlZE=
		</data>
		<key>MaterialIcons.ttf</key>
		<data>
		GUOAPWUnUMjsYEFfHldAHHDnLSU=
		</data>
		<key>MuseoSans-100.otf</key>
		<data>
		SdNRsPBtu6HaeHiZdvM8Ksq86Ss=
		</data>
		<key>MuseoSans-100Italic.otf</key>
		<data>
		ESJKe3z+BnNI5u9hkbmshaKO1q0=
		</data>
		<key>MuseoSans-300.otf</key>
		<data>
		c9/FzOJ8jXFuXBSNsNTWybH0ZLU=
		</data>
		<key>MuseoSans-300Italic.otf</key>
		<data>
		/TwO6YJUBpgHyA9jCtJ/5zMyFjY=
		</data>
		<key>MuseoSans-700Italic.otf</key>
		<data>
		hgPZxte1yb2kQ8KuRaRI0pie86U=
		</data>
		<key>MuseoSans-900Italic.otf</key>
		<data>
		egU6yHeVxSTIJeyGCoGEw/054ro=
		</data>
		<key>MuseoSans_500.otf</key>
		<data>
		vQzMBlZfTMQ4QhvW8QFFaBWSUDQ=
		</data>
		<key>MuseoSans_500_Italic.otf</key>
		<data>
		C9NbaFaOiMAx5KmCHraHZLlIpr8=
		</data>
		<key>MuseoSans_700.otf</key>
		<data>
		aKn9uuY+SAICCsHFuCUVx3vFOZ0=
		</data>
		<key>MuseoSans_900.otf</key>
		<data>
		wABCLrUj5DcQtJlR69I13r0XkKk=
		</data>
		<key>Octicons.ttf</key>
		<data>
		KIdYdEE1A7zAgQ6ipE3BuDEsZlo=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>SimpleLineIcons.ttf</key>
		<data>
		n/uBpaEREuKS8swyPphIa61ZdZk=
		</data>
		<key>Zocial.ttf</key>
		<data>
		C9pZx7zT05J94EfgkYdaRCi/0as=
		</data>
		<key>assets/node_modules/react-native-ratings/src/images/airbnb-star-selected.png</key>
		<data>
		hmf3UuKfPUS+rQV3VCRSlOincJU=
		</data>
		<key>assets/node_modules/react-native-ratings/src/images/airbnb-star.png</key>
		<data>
		6+SZFOypfbZrRIyeiqg37A1ERt4=
		</data>
		<key>assets/node_modules/react-native-ratings/src/images/bell.png</key>
		<data>
		mRk2D6wOdyusOvkEInbyL3K/E6k=
		</data>
		<key>assets/node_modules/react-native-ratings/src/images/heart.png</key>
		<data>
		rhZVvqRdTKoU0IrsdqQezFd0zb8=
		</data>
		<key>assets/node_modules/react-native-ratings/src/images/rocket.png</key>
		<data>
		mqOYKD+48iI8i2r6/LHKVlm0RGw=
		</data>
		<key>assets/node_modules/react-native-ratings/src/images/star.png</key>
		<data>
		XPFaCu1j/HeeE7zoK38ulP635qc=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/AntDesign.json</key>
		<data>
		PoJveci96zTSptK+VgCJdZJER5A=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Entypo.json</key>
		<data>
		vP+/QMl+LCeJCh5NxZt7W2fRTkk=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/EvilIcons.json</key>
		<data>
		ljsW3QZD8lzjigA6/o4zfFZagqM=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Feather.json</key>
		<data>
		J17YV/qOIUHnMiL5l/EObl2NEe0=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/FontAwesome.json</key>
		<data>
		ZcNOPzPLU5pQFhW2jx8wZYjmNpI=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Foundation.json</key>
		<data>
		Ai42gALv1dnRb0630jFu/ei9k7Y=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Ionicons.json</key>
		<data>
		Q4dkqfy/5CTEjaqDVu+tcyl89pI=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/MaterialCommunityIcons.json</key>
		<data>
		6DneEMn761Q7+VswHmzqd+aBxMM=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/MaterialIcons.json</key>
		<data>
		GxHc1Di9GnpNWGY4KNmxzB1sGII=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Octicons.json</key>
		<data>
		HEfQl9CbtMlZqIFlOOz1nf+IM5A=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/SimpleLineIcons.json</key>
		<data>
		In54DUZJuzMm9BD6rbP2iYXyRcc=
		</data>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Zocial.json</key>
		<data>
		C5nCU3RLmwBBTeqKR7SngR16ro0=
		</data>
		<key>assets/node_modules/react-navigation-stack/lib/module/views/assets/back-icon-mask.png</key>
		<data>
		jpFiMACcZJsxxIbM+ov9qJ/Euvw=
		</data>
		<key>assets/node_modules/react-navigation-stack/lib/module/views/assets/back-icon.png</key>
		<data>
		keLOi1inJdXs0IRSKnXqyhW57Aw=
		</data>
		<key>assets/node_modules/react-navigation-stack/lib/module/views/assets/<EMAIL></key>
		<data>
		rSvG8m4uPMSItIcsIF+N2tDJD8U=
		</data>
		<key>assets/node_modules/react-navigation-stack/lib/module/views/assets/<EMAIL></key>
		<data>
		8kFG8Vv7Hfrw4yVcFrhUhbHieBA=
		</data>
		<key>assets/src/img/arrow-left.png</key>
		<data>
		ol4dpl6aZAzV0NakubcaUMk0C4I=
		</data>
		<key>assets/src/img/bike-light.jpg</key>
		<data>
		WGdYc7GxGwqGVitKBOoiIocknRs=
		</data>
		<key>assets/src/img/bolt-shirt.jpg</key>
		<data>
		r3e2SEsFg9pM+ocqo+4rc9L3xas=
		</data>
		<key>assets/src/img/filter-button.png</key>
		<data>
		Cs4RSATCYi7Ap+ObuE+RGpOlJs0=
		</data>
		<key>assets/src/img/footer-swagbot.png</key>
		<data>
		M0Awp1ae5fV+kmUxjAiNgLgkS6g=
		</data>
		<key>assets/src/img/login-bot.png</key>
		<data>
		Jy7v7OzFK+bIA6BHz9U9mVLCQnA=
		</data>
		<key>assets/src/img/menu-button.png</key>
		<data>
		GaG8qu8K3cDxmeCffvmLj0twj9I=
		</data>
		<key>assets/src/img/menu-cart.png</key>
		<data>
		bvkDmdrQZE+3q6izNb0ITmRo0yU=
		</data>
		<key>assets/src/img/menu-close.png</key>
		<data>
		VAa/l7vnKmoP1UHMBsFxNdFxm70=
		</data>
		<key>assets/src/img/pony-express.png</key>
		<data>
		IqpnUEIc0jvDA5S0SJmrojLL7j4=
		</data>
		<key>assets/src/img/red-onesie.jpg</key>
		<data>
		CSkceDAsur3a+aWfpNi3lEpIEvM=
		</data>
		<key>assets/src/img/red-tatt.jpg</key>
		<data>
		kFu9aKuVUbNf2SVudA3BbvMzcRQ=
		</data>
		<key>assets/src/img/sauce-backpack.jpg</key>
		<data>
		nDYv4DNGVBhPAZWhNx5kD/AZSJY=
		</data>
		<key>assets/src/img/sauce-bolt.png</key>
		<data>
		1OvYbLe8fkyrX7KatUNBeQtJqsk=
		</data>
		<key>assets/src/img/sauce-pullover.jpg</key>
		<data>
		KZhN+qITABIpSxDrmDIBQTO/nMU=
		</data>
		<key>assets/src/img/sl-404.jpg</key>
		<data>
		vqNNFTfPmrctHSeh7yG9u2GR+xc=
		</data>
		<key>assets/src/img/surfing-sauce.jpg</key>
		<data>
		rueOUDtNVmezKZPkXFPEMQurarE=
		</data>
		<key>assets/src/img/swag-labs-logo.png</key>
		<data>
		slWS0g3HbE64gIvqD1NnR/tNGfU=
		</data>
		<key>assets/src/img/toggle-grid.png</key>
		<data>
		00mZSv6TVear9ezNCXxl3EDKg6Y=
		</data>
		<key>assets/src/img/toggle-row.png</key>
		<data>
		JZOh08ZEvw8nBg+jWGlcFi/+4KQ=
		</data>
		<key>assets/src/js/app.json</key>
		<data>
		10j99k42DTSsgRL12RuK4PshzQ4=
		</data>
		<key>main.jsbundle</key>
		<data>
		F6I47n44KOA9uKirSIiN+RmphBU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AntDesign.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			TneGhDkoD7Q01Gl8e5EScUBsgfM=
			</data>
			<key>hash2</key>
			<data>
			eVXKFBJ7MEEsEU6xPP1wK12rJl/rGIDRaV1zyqgkzeE=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Vn9fQC6unaAr3pkWsxkm4yEjLz8=
			</data>
			<key>hash2</key>
			<data>
			2qJZFkUiSFcQhVeyc/KUZ+4ec5YMSa6K56enKDZFYoQ=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			OGQaKHg5puh3lbk0I7YL6HilIcI=
			</data>
			<key>hash2</key>
			<data>
			4/mUMBqLzTstoKMuEDirECTRihomMo0adgupbAmXoxw=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			0//hZOEFTymmiSE0mVCEdJT7zoE=
			</data>
			<key>hash2</key>
			<data>
			FGPf0y4PM+fLOOlsC6cVN+dYdoaVZCPbMQsApiWcJpg=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			DmnHyAO1lKLoecP4nqkx5CDwo00=
			</data>
			<key>hash2</key>
			<data>
			L0ioidZvulcwVAppn3id0ThnVVpnUY96YH+LX37m7+I=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			zODOlcghFq0YVsH/r+R6OwuouhM=
			</data>
			<key>hash2</key>
			<data>
			A2LaLf2+K7+4l9eJGkIY6CAvNw9fhp5tFvaIRpTYHUU=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			k73LnAljYKnbGanUkF+Uh7mKEtM=
			</data>
			<key>hash2</key>
			<data>
			VeCl9jcWcGwvry/J8w1zJsZuRRN3TDmBgRGlunvgzWc=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			k73LnAljYKnbGanUkF+Uh7mKEtM=
			</data>
			<key>hash2</key>
			<data>
			VeCl9jcWcGwvry/J8w1zJsZuRRN3TDmBgRGlunvgzWc=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			IYs1eJJIvE3QyKp5ZOYijyN8JLQ=
			</data>
			<key>hash2</key>
			<data>
			cpypu/uoSMioFwVuD6p+uY3fTHzunCJ7XNv+T8ennUA=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			XZgFFu7FpLx625lKCyyBVFE9dvI=
			</data>
			<key>hash2</key>
			<data>
			Di8dQsInPum1z/W0uAq4T3zzJQm4teVVjeWByqBFLRA=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.nib</key>
		<dict>
			<key>hash</key>
			<data>
			I4rS2bLRdmCHzIxaCTnwfnV/re0=
			</data>
			<key>hash2</key>
			<data>
			TzmfbaFXPg8HGfZqMNjPkrDEj+56RYzmnmF3jQMDx1E=
			</data>
		</dict>
		<key>Entypo.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			R4cKZ91Qhxdl95nKqX2ochnuR8U=
			</data>
			<key>hash2</key>
			<data>
			3QhJoVkfiNp5N7I/kiQYpc7FTgdeCePMyo/rYgFvqoI=
			</data>
		</dict>
		<key>EvilIcons.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			kdN36jz0dJCyVsLtCBcEp9q9rgw=
			</data>
			<key>hash2</key>
			<data>
			pcrrTTlcXjLx1aMKyzgq68Zk8brf0UkxmQfyIV5OPiY=
			</data>
		</dict>
		<key>Feather.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			Um4D6mimHEpnpwsB17NF6rNJdn0=
			</data>
			<key>hash2</key>
			<data>
			RtQXlhv1OBMGa9LExQGx+R+SVg01HG18MeJ9xp7A7cc=
			</data>
		</dict>
		<key>FontAwesome.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			E7HqtlqYPHpzvHmXxHnWaUP3xss=
			</data>
			<key>hash2</key>
			<data>
			qljzPyOaD7AvXHpsRcBD16msmgkzNYBmlOzW1O3A1qg=
			</data>
		</dict>
		<key>FontAwesome5_Brands.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			Zshu39E+Y+Dpgc6FMzXCi8LSDOw=
			</data>
			<key>hash2</key>
			<data>
			oMFFOCrP5Zdn0Uh9ZQXzSWZbaFwohc2RtiHRQaKSsPg=
			</data>
		</dict>
		<key>FontAwesome5_Regular.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			APVp99isJJekOyXcTTsyITvOoxs=
			</data>
			<key>hash2</key>
			<data>
			8yuUopBg3+wMeXod/hETV+sdaC8aykrEuVzw9MPiv6k=
			</data>
		</dict>
		<key>FontAwesome5_Solid.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			/483360QV5a9kk0NsNN6T/JSYYw=
			</data>
			<key>hash2</key>
			<data>
			Ix1ozvvGhG+3jK/KhGdAGk83HiJkGGYuiQDj1SrcAKs=
			</data>
		</dict>
		<key>Fontisto.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			wJCj7Jaj8bubYVwvPyBM4Nzc28M=
			</data>
			<key>hash2</key>
			<data>
			lNq58dWxPqB2nRJKLaDQJDPzuZ1t6loHwERqdxWBAMA=
			</data>
		</dict>
		<key>Foundation.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			SyvObHkkk6SlcWtv7C2+/olJLD8=
			</data>
			<key>hash2</key>
			<data>
			fh3QPdTOkLZYBSVUzXRZ3xZxZxc4mlUvpMbVal+JM+Y=
			</data>
		</dict>
		<key>Frameworks/libswiftCore.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			rrAp64iPhUL40XQR7JlGMDIZaMA=
			</data>
			<key>hash2</key>
			<data>
			SVCZK2UG9ihtWggcfIn/i94g//jyC4cI73gURFdHavA=
			</data>
		</dict>
		<key>Frameworks/libswiftCoreFoundation.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			P4IT9OqWCWZ8FyecMxQHhM6h4G8=
			</data>
			<key>hash2</key>
			<data>
			/wz85l+DKAaQ7/BS+77vK5X998iA4Lwz+DGJ95g4kZw=
			</data>
		</dict>
		<key>Frameworks/libswiftCoreGraphics.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			JoDjsNQXE3z2hogOouALtjw8m/g=
			</data>
			<key>hash2</key>
			<data>
			6UmJ5TyRJe3XQ2ANvUh7wapWfH50HDaC5DE8brYBB5U=
			</data>
		</dict>
		<key>Frameworks/libswiftCoreImage.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			dccNAYDUBxOzns3c+W4XRSmf1Ps=
			</data>
			<key>hash2</key>
			<data>
			yd4z45rFP5ibtGizpvCVNJ3AoMhTSP9AnIyUKk/JVGc=
			</data>
		</dict>
		<key>Frameworks/libswiftCoreLocation.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			GMi1JxCYZe10XWexHCK2KQ5T2Lo=
			</data>
			<key>hash2</key>
			<data>
			I430RKmMGhsrGAnn25OPS3EcLck+xsi9NrCX2HT5gTw=
			</data>
		</dict>
		<key>Frameworks/libswiftDarwin.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			XJGVexKAsC220QzBQHwL6VS/whc=
			</data>
			<key>hash2</key>
			<data>
			Fy3X7nPk7IgG+lCSWKHuMHgGz6Yi56UOKe6XajzinhU=
			</data>
		</dict>
		<key>Frameworks/libswiftDispatch.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			xYRhwsR1eeXatGDr8WNyVj93krw=
			</data>
			<key>hash2</key>
			<data>
			ZSH1H7EN5E4TGGB63N2+7rR7N+rDR1LBmkhABOf9RVQ=
			</data>
		</dict>
		<key>Frameworks/libswiftFoundation.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			WclOTQyDfPDOGEjZboBz+yfxGVQ=
			</data>
			<key>hash2</key>
			<data>
			6SyHvvl3dAOzLH6bFEeF41TPcUHS3iYOdFK7I0FEU6M=
			</data>
		</dict>
		<key>Frameworks/libswiftMetal.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			q6qpoqLxtRrru4zloX0b0xHIJVw=
			</data>
			<key>hash2</key>
			<data>
			Y51e3klbWCwDOKopGL7Z5cieD4LriiLn9IJFA0FvLTs=
			</data>
		</dict>
		<key>Frameworks/libswiftObjectiveC.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			W4v+YlroD8+v27M4zKa/3r9eqdA=
			</data>
			<key>hash2</key>
			<data>
			zLWBGbdbTQk6chVChX0c8CFwhq8ogd9K00QEU5AufEY=
			</data>
		</dict>
		<key>Frameworks/libswiftQuartzCore.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			OzOL7prTxnOJwhTRps72jdhagI4=
			</data>
			<key>hash2</key>
			<data>
			zB0hL5hCB758QPCoQtMOfmCnOq42iXWoTwQdcBU2G1U=
			</data>
		</dict>
		<key>Frameworks/libswiftUIKit.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			G10/WJel9vPNJjANFC1WEM1Jno0=
			</data>
			<key>hash2</key>
			<data>
			sio8OcXJb8oJVNHzTWsEzXEpJrNWpQmF06EEIQKl26k=
			</data>
		</dict>
		<key>Frameworks/libswiftos.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			oMpvzHAVfevcqm0jDkhUpqLzhXg=
			</data>
			<key>hash2</key>
			<data>
			KnKo8oZ57w7czhNeCWrd3UlEmiHIpnQ5qjzRz88lPuE=
			</data>
		</dict>
		<key>Ionicons.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			tSgfv6QxKavD5BLdk4z88Ld96Zk=
			</data>
			<key>hash2</key>
			<data>
			7rACslkR31LsyqT1swN3jtf0ipsHhSYwVR6fFcxwnzM=
			</data>
		</dict>
		<key>MaterialCommunityIcons.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			gKQx1tSYYdZVAJ6UkUCSgtJVlZE=
			</data>
			<key>hash2</key>
			<data>
			biEyi7cPCdqSjAM95zaIItliUKHM0qFqb0fedqPMYb0=
			</data>
		</dict>
		<key>MaterialIcons.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			GUOAPWUnUMjsYEFfHldAHHDnLSU=
			</data>
			<key>hash2</key>
			<data>
			xrFho4+y/siypSIlLQPJBzVc2RvMKOGd/dpfM1q+8Fg=
			</data>
		</dict>
		<key>MuseoSans-100.otf</key>
		<dict>
			<key>hash</key>
			<data>
			SdNRsPBtu6HaeHiZdvM8Ksq86Ss=
			</data>
			<key>hash2</key>
			<data>
			aOO3+VQVZ0YF0Cm47j3rduTrxxvT3+T0+e/8gOGPtoU=
			</data>
		</dict>
		<key>MuseoSans-100Italic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			ESJKe3z+BnNI5u9hkbmshaKO1q0=
			</data>
			<key>hash2</key>
			<data>
			qgHaTYlVLMDIgPf7hH9dwT4jqca4qlPiH/Gr+ckgnqU=
			</data>
		</dict>
		<key>MuseoSans-300.otf</key>
		<dict>
			<key>hash</key>
			<data>
			c9/FzOJ8jXFuXBSNsNTWybH0ZLU=
			</data>
			<key>hash2</key>
			<data>
			7MJPQPVlzj2GP0qw/jJYxtksp5Z3akyufWj7Uv3d630=
			</data>
		</dict>
		<key>MuseoSans-300Italic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			/TwO6YJUBpgHyA9jCtJ/5zMyFjY=
			</data>
			<key>hash2</key>
			<data>
			ETgHTklVQqZg8SzOEJo+Iu8sc0RIELFMG4wQ9msOi38=
			</data>
		</dict>
		<key>MuseoSans-700Italic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			hgPZxte1yb2kQ8KuRaRI0pie86U=
			</data>
			<key>hash2</key>
			<data>
			3xD4jVlHpRF9buzcntcxlwAXMVfdb4M5h6uxCyF1PX4=
			</data>
		</dict>
		<key>MuseoSans-900Italic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			egU6yHeVxSTIJeyGCoGEw/054ro=
			</data>
			<key>hash2</key>
			<data>
			K0ojmRQhSr909fWh27/GNBnmdwDQexwUbity9dfSVNI=
			</data>
		</dict>
		<key>MuseoSans_500.otf</key>
		<dict>
			<key>hash</key>
			<data>
			vQzMBlZfTMQ4QhvW8QFFaBWSUDQ=
			</data>
			<key>hash2</key>
			<data>
			RkKPLFOe7Miwb+y36nTcj5Rf2asluLTKu6GqVfbZEjk=
			</data>
		</dict>
		<key>MuseoSans_500_Italic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			C9NbaFaOiMAx5KmCHraHZLlIpr8=
			</data>
			<key>hash2</key>
			<data>
			H3/oPHA+WrB6XEmKyAxF1qSNFMxoU9HFucA4K2lq/vo=
			</data>
		</dict>
		<key>MuseoSans_700.otf</key>
		<dict>
			<key>hash</key>
			<data>
			aKn9uuY+SAICCsHFuCUVx3vFOZ0=
			</data>
			<key>hash2</key>
			<data>
			w2yozVVmwVbiPzjd5V76l2cnDHMt3LftkV6kSyKVYB4=
			</data>
		</dict>
		<key>MuseoSans_900.otf</key>
		<dict>
			<key>hash</key>
			<data>
			wABCLrUj5DcQtJlR69I13r0XkKk=
			</data>
			<key>hash2</key>
			<data>
			NSNzSqTlwlUl1JRwTmRf8Ir2e398d63bDWozA9CKVFo=
			</data>
		</dict>
		<key>Octicons.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			KIdYdEE1A7zAgQ6ipE3BuDEsZlo=
			</data>
			<key>hash2</key>
			<data>
			ua3cE7d8ZyDNSM0Y8PSoyuWJsbkQNjDCSd5gq3Lf1r4=
			</data>
		</dict>
		<key>SimpleLineIcons.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			n/uBpaEREuKS8swyPphIa61ZdZk=
			</data>
			<key>hash2</key>
			<data>
			P1Ad2wXHCCm7tRz+nKn/9X854GBFfCV7PM8l33Z/CHA=
			</data>
		</dict>
		<key>Zocial.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			C9pZx7zT05J94EfgkYdaRCi/0as=
			</data>
			<key>hash2</key>
			<data>
			17EKHr4YMOWi8I9JA7moDiwZEsDdL3O5P88r+DIhZgI=
			</data>
		</dict>
		<key>assets/node_modules/react-native-ratings/src/images/airbnb-star-selected.png</key>
		<dict>
			<key>hash</key>
			<data>
			hmf3UuKfPUS+rQV3VCRSlOincJU=
			</data>
			<key>hash2</key>
			<data>
			0uhqbkGIEMvafcWJq5WIN7foj1Rcpj1I+kGPldGZk+M=
			</data>
		</dict>
		<key>assets/node_modules/react-native-ratings/src/images/airbnb-star.png</key>
		<dict>
			<key>hash</key>
			<data>
			6+SZFOypfbZrRIyeiqg37A1ERt4=
			</data>
			<key>hash2</key>
			<data>
			oVsAeqR/oxXBOFSgLKve75iROK54H/Jn3f51b4ADsMg=
			</data>
		</dict>
		<key>assets/node_modules/react-native-ratings/src/images/bell.png</key>
		<dict>
			<key>hash</key>
			<data>
			mRk2D6wOdyusOvkEInbyL3K/E6k=
			</data>
			<key>hash2</key>
			<data>
			MVdJITYj1tDwle06K+p8IFp8haF5kBT3gxuzDfc9AYg=
			</data>
		</dict>
		<key>assets/node_modules/react-native-ratings/src/images/heart.png</key>
		<dict>
			<key>hash</key>
			<data>
			rhZVvqRdTKoU0IrsdqQezFd0zb8=
			</data>
			<key>hash2</key>
			<data>
			v2rfzDt3/cDiJKjdhP8ejuEJZF2q6bsySGtwzOb8AxY=
			</data>
		</dict>
		<key>assets/node_modules/react-native-ratings/src/images/rocket.png</key>
		<dict>
			<key>hash</key>
			<data>
			mqOYKD+48iI8i2r6/LHKVlm0RGw=
			</data>
			<key>hash2</key>
			<data>
			ICOGuBMxnevPKkpTB/Aa5C5FHGGdwlrVPfiIpgRfeqk=
			</data>
		</dict>
		<key>assets/node_modules/react-native-ratings/src/images/star.png</key>
		<dict>
			<key>hash</key>
			<data>
			XPFaCu1j/HeeE7zoK38ulP635qc=
			</data>
			<key>hash2</key>
			<data>
			QomdN3rBYNJR6hzK8VcKZmuxkf8KClRArNMi+Fxau5U=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/AntDesign.json</key>
		<dict>
			<key>hash</key>
			<data>
			PoJveci96zTSptK+VgCJdZJER5A=
			</data>
			<key>hash2</key>
			<data>
			/K4o5ABMKk56TLGP9qpNDgFn1mwlGCKdioUTnutStos=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Entypo.json</key>
		<dict>
			<key>hash</key>
			<data>
			vP+/QMl+LCeJCh5NxZt7W2fRTkk=
			</data>
			<key>hash2</key>
			<data>
			9WMXSgpVzwfQ+b7WCRnX3lyo619xEE06qE3gfFk2v4A=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/EvilIcons.json</key>
		<dict>
			<key>hash</key>
			<data>
			ljsW3QZD8lzjigA6/o4zfFZagqM=
			</data>
			<key>hash2</key>
			<data>
			QS0QTL6magSjGJDdBXEYhSM0jhhJbKJTY1TRMTKLnB8=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Feather.json</key>
		<dict>
			<key>hash</key>
			<data>
			J17YV/qOIUHnMiL5l/EObl2NEe0=
			</data>
			<key>hash2</key>
			<data>
			wTODHcxcyKkP3M1qrbEpYQbWABbqZAml9jrD72v8TP4=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/FontAwesome.json</key>
		<dict>
			<key>hash</key>
			<data>
			ZcNOPzPLU5pQFhW2jx8wZYjmNpI=
			</data>
			<key>hash2</key>
			<data>
			l8SY1ctoXtJ8rk57TurG259sjQSsHZWLcPxtvBdp6i8=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Foundation.json</key>
		<dict>
			<key>hash</key>
			<data>
			Ai42gALv1dnRb0630jFu/ei9k7Y=
			</data>
			<key>hash2</key>
			<data>
			vsMuPq7HtNBlCb3u3/SWs6r2nkJhr07ifMXgnjLctUs=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Ionicons.json</key>
		<dict>
			<key>hash</key>
			<data>
			Q4dkqfy/5CTEjaqDVu+tcyl89pI=
			</data>
			<key>hash2</key>
			<data>
			F5swEFKG+3/SVEAUdmY52DQ8N+0d8PYm1N1B4C75DPM=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/MaterialCommunityIcons.json</key>
		<dict>
			<key>hash</key>
			<data>
			6DneEMn761Q7+VswHmzqd+aBxMM=
			</data>
			<key>hash2</key>
			<data>
			9jx2bjcrpuWRxJoAjmAJIFOtCFUBs7rOEW7mX5BxWjg=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/MaterialIcons.json</key>
		<dict>
			<key>hash</key>
			<data>
			GxHc1Di9GnpNWGY4KNmxzB1sGII=
			</data>
			<key>hash2</key>
			<data>
			FE57IqkgiYbcwyhtHhchRH5UoqP4meXdTj3VWaWGC/w=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Octicons.json</key>
		<dict>
			<key>hash</key>
			<data>
			HEfQl9CbtMlZqIFlOOz1nf+IM5A=
			</data>
			<key>hash2</key>
			<data>
			k1Tfyb9RtPyq/hM/bijGjh+0yn771WQ6XjQSZEsGMwg=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/SimpleLineIcons.json</key>
		<dict>
			<key>hash</key>
			<data>
			In54DUZJuzMm9BD6rbP2iYXyRcc=
			</data>
			<key>hash2</key>
			<data>
			NNgXY0eexBvoQ9JgEuHvBnMbMfbp7TcfBU91KUJRqGc=
			</data>
		</dict>
		<key>assets/node_modules/react-native-vector-icons/glyphmaps/Zocial.json</key>
		<dict>
			<key>hash</key>
			<data>
			C5nCU3RLmwBBTeqKR7SngR16ro0=
			</data>
			<key>hash2</key>
			<data>
			MzE2mK2LBPdazLJY9iVDcGOy6FOU17HltkCa3IV7OdY=
			</data>
		</dict>
		<key>assets/node_modules/react-navigation-stack/lib/module/views/assets/back-icon-mask.png</key>
		<dict>
			<key>hash</key>
			<data>
			jpFiMACcZJsxxIbM+ov9qJ/Euvw=
			</data>
			<key>hash2</key>
			<data>
			3BOFxJPrVIOr2o/UlYerJGXuSikHgRJCsx/hu/CYWX4=
			</data>
		</dict>
		<key>assets/node_modules/react-navigation-stack/lib/module/views/assets/back-icon.png</key>
		<dict>
			<key>hash</key>
			<data>
			keLOi1inJdXs0IRSKnXqyhW57Aw=
			</data>
			<key>hash2</key>
			<data>
			LN/rjlzN55dvcBL7jM5zryIpAp7iHU8VCf7RFIccbNg=
			</data>
		</dict>
		<key>assets/node_modules/react-navigation-stack/lib/module/views/assets/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			rSvG8m4uPMSItIcsIF+N2tDJD8U=
			</data>
			<key>hash2</key>
			<data>
			K3RDqaWOksoRpXW9wRWYYVpX3eMFzYznDWAmBrMCzZg=
			</data>
		</dict>
		<key>assets/node_modules/react-navigation-stack/lib/module/views/assets/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			8kFG8Vv7Hfrw4yVcFrhUhbHieBA=
			</data>
			<key>hash2</key>
			<data>
			Pd0Hc+0n4j0leJ8wFzGzrEVa2L5aonoKG4OPicbSciU=
			</data>
		</dict>
		<key>assets/src/img/arrow-left.png</key>
		<dict>
			<key>hash</key>
			<data>
			ol4dpl6aZAzV0NakubcaUMk0C4I=
			</data>
			<key>hash2</key>
			<data>
			l2GrNs40P37dq2tR9TE1NISc8wHi1QbhdkTOeOCZtSU=
			</data>
		</dict>
		<key>assets/src/img/bike-light.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			WGdYc7GxGwqGVitKBOoiIocknRs=
			</data>
			<key>hash2</key>
			<data>
			DFj2O0epbc87hrlVSvjzpq/DYiMkleSmo5Fvp/D9ako=
			</data>
		</dict>
		<key>assets/src/img/bolt-shirt.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			r3e2SEsFg9pM+ocqo+4rc9L3xas=
			</data>
			<key>hash2</key>
			<data>
			y2ZEEBxoKnSYFx8nHjtjpbklzDN7hhezt1rPAzPEnQo=
			</data>
		</dict>
		<key>assets/src/img/filter-button.png</key>
		<dict>
			<key>hash</key>
			<data>
			Cs4RSATCYi7Ap+ObuE+RGpOlJs0=
			</data>
			<key>hash2</key>
			<data>
			OyNy7MSxfoaBmozRxwJQLeRKrfvkJyw3oVSriuy+CTE=
			</data>
		</dict>
		<key>assets/src/img/footer-swagbot.png</key>
		<dict>
			<key>hash</key>
			<data>
			M0Awp1ae5fV+kmUxjAiNgLgkS6g=
			</data>
			<key>hash2</key>
			<data>
			W5U7hZGfBlVm7MqaU/AqPoMuayfKg64FRRK/IklhX7k=
			</data>
		</dict>
		<key>assets/src/img/login-bot.png</key>
		<dict>
			<key>hash</key>
			<data>
			Jy7v7OzFK+bIA6BHz9U9mVLCQnA=
			</data>
			<key>hash2</key>
			<data>
			gC6FcZ9JOTh/bY0v5uNcdR5M8bSfeNgtyPxScqaQ5Ik=
			</data>
		</dict>
		<key>assets/src/img/menu-button.png</key>
		<dict>
			<key>hash</key>
			<data>
			GaG8qu8K3cDxmeCffvmLj0twj9I=
			</data>
			<key>hash2</key>
			<data>
			CbjNO+Id2KRepDk0Wm2Gm7hUGKkXg1ojVFwRnKSl2fg=
			</data>
		</dict>
		<key>assets/src/img/menu-cart.png</key>
		<dict>
			<key>hash</key>
			<data>
			bvkDmdrQZE+3q6izNb0ITmRo0yU=
			</data>
			<key>hash2</key>
			<data>
			sOJCnhmFoGci2Q+LxSIIt/l5N4keMkVTMTucLWBI1Gg=
			</data>
		</dict>
		<key>assets/src/img/menu-close.png</key>
		<dict>
			<key>hash</key>
			<data>
			VAa/l7vnKmoP1UHMBsFxNdFxm70=
			</data>
			<key>hash2</key>
			<data>
			+ALWjxIxfnz3soR1kiQTOKfMkiizKRNEpbhlNJN7v3Q=
			</data>
		</dict>
		<key>assets/src/img/pony-express.png</key>
		<dict>
			<key>hash</key>
			<data>
			IqpnUEIc0jvDA5S0SJmrojLL7j4=
			</data>
			<key>hash2</key>
			<data>
			mb0o8163eMdPZcD2YBrDW/WSg4dRqbOqUVkDAxB9rAA=
			</data>
		</dict>
		<key>assets/src/img/red-onesie.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			CSkceDAsur3a+aWfpNi3lEpIEvM=
			</data>
			<key>hash2</key>
			<data>
			FEljws84pEwwnxpNNrgjZ5DzbTsVlY7C3td9v43k/CM=
			</data>
		</dict>
		<key>assets/src/img/red-tatt.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			kFu9aKuVUbNf2SVudA3BbvMzcRQ=
			</data>
			<key>hash2</key>
			<data>
			jxWIpLOmV+CwRWH67pIxKxSheGSQiFfkBEMZQhuUWCc=
			</data>
		</dict>
		<key>assets/src/img/sauce-backpack.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			nDYv4DNGVBhPAZWhNx5kD/AZSJY=
			</data>
			<key>hash2</key>
			<data>
			ualHAddYBaEBR5WDsDel1hjTQbSfn7cb3ivu6uxQg9k=
			</data>
		</dict>
		<key>assets/src/img/sauce-bolt.png</key>
		<dict>
			<key>hash</key>
			<data>
			1OvYbLe8fkyrX7KatUNBeQtJqsk=
			</data>
			<key>hash2</key>
			<data>
			KlRKvzhqAnIyoGYAj3ibkQ0D3AW5lPOFzdJLBw5VWWs=
			</data>
		</dict>
		<key>assets/src/img/sauce-pullover.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			KZhN+qITABIpSxDrmDIBQTO/nMU=
			</data>
			<key>hash2</key>
			<data>
			UIhTkfK750f1XA2gTv6zLcmNYlZeDySjJlUZj1CVq1s=
			</data>
		</dict>
		<key>assets/src/img/sl-404.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			vqNNFTfPmrctHSeh7yG9u2GR+xc=
			</data>
			<key>hash2</key>
			<data>
			mInVl7kRi0dM+X+3Gi6gu+sSq/KaGKIWVQUG6TTG0Nk=
			</data>
		</dict>
		<key>assets/src/img/surfing-sauce.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			rueOUDtNVmezKZPkXFPEMQurarE=
			</data>
			<key>hash2</key>
			<data>
			r3wReV4ZhpSgOpnCu2dRDgWhQUKAYiXwnG6i3D2HtyI=
			</data>
		</dict>
		<key>assets/src/img/swag-labs-logo.png</key>
		<dict>
			<key>hash</key>
			<data>
			slWS0g3HbE64gIvqD1NnR/tNGfU=
			</data>
			<key>hash2</key>
			<data>
			+2UJabqp9MC+l37JENLllmSt13Q/kASYjczcOqcg51U=
			</data>
		</dict>
		<key>assets/src/img/toggle-grid.png</key>
		<dict>
			<key>hash</key>
			<data>
			00mZSv6TVear9ezNCXxl3EDKg6Y=
			</data>
			<key>hash2</key>
			<data>
			MxC8nfciDDUutOdBpBKfmAKz822u30qWWomC7iDK8nU=
			</data>
		</dict>
		<key>assets/src/img/toggle-row.png</key>
		<dict>
			<key>hash</key>
			<data>
			JZOh08ZEvw8nBg+jWGlcFi/+4KQ=
			</data>
			<key>hash2</key>
			<data>
			3O0lDD2wj33PgWWO0lPKsPAc5ru/IOuzc5M8A/Plou0=
			</data>
		</dict>
		<key>assets/src/js/app.json</key>
		<dict>
			<key>hash</key>
			<data>
			10j99k42DTSsgRL12RuK4PshzQ4=
			</data>
			<key>hash2</key>
			<data>
			37G14w8tJTdTQf2wc0GDa8Wb7ZxrxhPdEw89rspRh/I=
			</data>
		</dict>
		<key>main.jsbundle</key>
		<dict>
			<key>hash</key>
			<data>
			F6I47n44KOA9uKirSIiN+RmphBU=
			</data>
			<key>hash2</key>
			<data>
			74WUBxlA/UIer/FmExafRnfjfHzayY22ThW0aT3H0Ko=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
