let { join } = require('path');
const video = require('wdio-video-reporter');

const todayBuild = new Date().toLocaleString('en-US', { weekday: 'long' });

exports.config = {
    //
    // ====================
    // Runner Configuration
    // ====================
    //
    // WebdriverIO allows it to run your tests in arbitrary locations (e.g. locally or
    // on a remote machine).
    path: '/wd/hub',
    runner: 'local',
    port: 4723,
    //
    // ==================
    // Specify Test Files
    // ==================
    // Define which test specs should run. The pattern is relative to the directory
    // from which `wdio` was called. Notice that, if you are calling `wdio` from an
    // NPM script (see https://docs.npmjs.com/cli/run-script) then the current working
    // directory is where your package.json resides, so `wdio` will be called from there.
    //
    specs: [
        './test/specs/**/*.ts'
    ],
    // Patterns to exclude.
    exclude: [
        // 'path/to/excluded/files'
    ],
    //
    // ============
    // Capabilities
    // ============
    // Define your capabilities here. WebdriverIO can run multiple capabilities at the same
    // time. Depending on the number of capabilities, WebdriverIO launches several test
    // sessions. Within your capabilities you can overwrite the spec and exclude options in
    // order to group specific specs to a specific capability.
    //
    // First, you can define how many instances should be started at the same time. Let's
    // say you have 3 different capabilities (Chrome, Firefox, and Safari) and you have
    // set maxInstances to 1; wdio will spawn 3 processes. Therefore, if you have 10 spec
    // files and you set maxInstances to 10, all spec files will get tested at the same time
    // and 30 processes will get spawned. The property handles how many capabilities
    // from the same test should run tests.
    //
    maxInstances: 1,
    //
    // If you have trouble getting all important capabilities together, check out the
    // Sauce Labs platform configurator - a great tool to configure your capabilities:
    // https://docs.saucelabs.com/reference/platforms-configurator
    
    capabilities: [
        {

        platformName: 'Android',
        'appium:platformVersion': '16.0',
        'appium:deviceName': 'Medium_Phone_API_36.0',
        'appium:app': join(process.cwd(), './Android.SauceLabs.Mobile.Sample.app.2.7.1.apk'),
        'appium:automationName': 'UiAutomator2',
        
        // Critical Android-specific capabilities to prevent hanging
        'appium:newCommandTimeout': 300,
        'appium:sessionOverride': true,
        'appium:autoGrantPermissions': true,
        'appium:noReset': false,
        'appium:fullReset': false,
        
        // App launch and activity settings
        'appium:appWaitActivity': '*', // Wait for any activity
        'appium:appWaitDuration': 30000,
        'appium:androidInstallTimeout': 120000,
        
        // Performance and stability settings
        'appium:uiautomator2ServerInstallTimeout': 60000,
        'appium:uiautomator2ServerLaunchTimeout': 60000,
        'appium:skipServerInstallation': false,
        'appium:skipDeviceInitialization': false,
        
        // Additional stability options
        'appium:disableWindowAnimation': true,
        'appium:skipUnlock': true,
        'appium:unlockType': 'pin',
        'appium:unlockKey': '1234', // Adjust if needed
        
        // Timeout settings
        'appium:androidDeviceReadyTimeout': 60,
        'appium:androidInstallTimeout': 90000,
        'df:options': {
            saveDeviceLogs: true,
            build: todayBuild,
            testName: 'Login Test Suite',
            projectName: 'Mobile App Testing'
        },
        'df:accesskey': 'admin_AzZvGSzPbIrrEx',
        'df:token': '9de28a88-21b5-4887-8282-848106f3afe5',
        'df:recordVideo': true,
        "df:videoTimeLimit": null

    }
    // {
    //     platformName: 'ios',
    //     'appium:deviceName': 'iPhone 16',
    //     'appium:automationName': 'XCUITest',
    //     'appium:app': join(process.cwd(), './iOS.Simulator.SauceLabs.Mobile.Sample.app.2.7.1.app'),
    //     'appium:noReset': false,
    //     'appium:udid': '5AD8A6FB-ADA4-4022-B114-743866B9946C',
    //     'df:recordVideo': true,
    //     'df:options': { saveDeviceLogs: true, build: todayBuild },
    //     'df:accesskey': 'admin_AzZvGSzPbIrrEx',
    //     'df:token': '9de28a88-21b5-4887-8282-848106f3afe5'
    // }
    // ,
    // {
    //     platformName: 'ios',
    //     'appium:deviceName': 'iPhone 16e',
    //     'appium:automationName': 'XCUITest',
    //     'appium:app': join(process.cwd(), './iOS.Simulator.SauceLabs.Mobile.Sample.app.2.7.1.app/SwagLabsMobileApp'),
    //     'appium:noReset': false,
    //     'appium:udid': '1DD7B829-313E-43FF-B202-32F2A906E6B6',
    //     'df:recordVideo': true,
    //     'df:options': { saveDeviceLogs: true, build: todayBuild },
    //     'df:accesskey': 'admin_nuCfsTwdNunb9B',
    //     'df:token': 'de798c95-e521-4f3e-b480-cebd373df02b'
    // }
],


    // ===================
    // Test Configurations
    // ===================
    // Define all options that are relevant for the WebdriverIO instance here
    //
    // Level of logging verbosity: trace | debug | info | warn | error | silent
    logLevel: 'error',
    //
    // Set specific log levels per logger
    // loggers:
    // - webdriver, webdriverio
    // - @wdio/applitools-service, @wdio/browserstack-service, @wdio/devtools-service, @wdio/sauce-service
    // - @wdio/mocha-framework, @wdio/jasmine-framework
    // - @wdio/local-runner, @wdio/lambda-runner
    // - @wdio/sumologic-reporter
    // - @wdio/cli, @wdio/config, @wdio/sync, @wdio/utils
    // Level of logging verbosity: trace | debug | info | warn | error | silent
    // logLevels: {
    //     webdriver: 'info',
    //     '@wdio/applitools-service': 'info'
    // },
    //
    // If you only want to run your tests until a specific amount of tests have failed use
    // bail (default is 0 - don't bail, run all tests).
    bail: 0,
    //
    // Set a base URL in order to shorten url command calls. If your `url` parameter starts
    // with `/`, the base url gets prepended, not including the path portion of your baseUrl.
    // If your `url` parameter starts without a scheme or `/` (like `some/path`), the base url
    // gets prepended directly.
    baseUrl: 'http://localhost',
    //
    // Default timeout for all waitFor* commands.
    waitforTimeout: 10000,
    //
    // Default timeout in milliseconds for request
    // if Selenium Grid doesn't send response
    connectionRetryTimeout: 90000,
    //
    // Default request retries count
    connectionRetryCount: 3,
    //
    // Test runner services
    // Services take over a specific job you don't want to take care of. They enhance
    // your test setup with almost no effort. Unlike plugins, they don't add new
    // commands. Instead, they hook themselves up into the test process.
    services: [
        ['appium', {
            command: 'appium',
        }]
    ],
    
    // Framework you want to run your specs with.
    // The following are supported: Mocha, Jasmine, and Cucumber
    // see also: https://webdriver.io/docs/frameworks.html
    //
    // Make sure you have the wdio adapter package for the specific framework installed
    // before running any tests.
    framework: 'mocha',
    //
    // The number of times to retry the entire specfile when it fails as a whole
    // specFileRetries: 1,
    //
    // Test reporter for stdout.
    // The only one supported by default is 'dot'
    // see also: https://webdriver.io/docs/dot-reporter.html
    reporters: [
    [
        'video',
        {
            saveAllVideos: true,
            videoSlowdownMultiplier: 1,
            outputDir: './_results_/',
            maxTestNameCharacters: 100,
            videoRenderTimeout: 5,
            excludeDriverActions: [],
            // Add these options to prevent screenshot errors
            usingBrowserstack: false,
            recordingPath: './_results_/',
        },
    ]
],

 
    //
    // Options to be passed to Mocha.
    // See the full list at http://mochajs.org/
    mochaOpts: {
        ui: 'bdd',
        timeout: 60000
    },
    //
    // =====
    // Hooks
    // =====
    // WebdriverIO provides several hooks you can use to interfere with the test process in order to enhance
    // it and to build services around it. You can either apply a single function or an array of
    // methods to it. If one of them returns with a promise, WebdriverIO will wait until that promise got
    // resolved to continue.
    /**
     * Gets executed once before all workers get launched.
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     */
    // onPrepare: function (config, capabilities) {
    // },
    /**
     * Gets executed just before initialising the webdriver session and test framework. It allows you
     * to manipulate configurations depending on the capability or spec.
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {Array.<String>} specs List of spec file paths that are to be run
     */
    // beforeSession: function (config, capabilities, specs) {
    // },
    /**
     * Gets executed before test execution begins. At this point you can access to all global
     * variables like `browser`. It is the perfect place to define custom commands.
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {Array.<String>} specs List of spec file paths that are to be run
     */
    before: function (capabilities, specs) {
        require('ts-node').register({
            project: 'tsconfig.json'
        });
    }, 
    /**
     * Runs before a WebdriverIO command gets executed.
     * @param {String} commandName hook command name
     * @param {Array} args arguments that command would receive
     */
    // beforeCommand: function (commandName, args) {
    // },
    /**
     * Hook that gets executed before the suite starts
     * @param {Object} suite suite details
     */
    // beforeSuite: function (capabilities, suite) {
    // },
    /**
     * Function to be executed before a test (in Mocha/Jasmine) starts.
     */
    beforeTest: function (test, context) {
        console.log(`Starting test: ${test.title} in suite: ${test.parent}`);

        // Try to set test metadata at the start
        try {
            if (driver && driver.sessionId) {
                // Log test start for Device Farm plugin to potentially pick up
                console.log(`DEVICE_FARM_TEST_START: ${JSON.stringify({
                    sessionId: driver.sessionId,
                    testName: test.title,
                    testSuite: test.parent,
                    timestamp: new Date().toISOString()
                })}`);
            }
        } catch (error) {
            console.log('Could not set test start metadata:', error.message);
        }
    },
    /**
     * Hook that gets executed _before_ a hook within the suite starts (e.g. runs before calling
     * beforeEach in Mocha)
     */
    // beforeHook: function (test, context) {
    // },
    /**
     * Hook that gets executed _after_ a hook within the suite starts (e.g. runs after calling
     * afterEach in Mocha)
     */
    // afterHook: function (test, context, { error, result, duration, passed, retries }) {
    // },
    /**
     * Function to be executed after a test (in Mocha/Jasmine).
     */
    afterTest: async function (test, context, { error, result, duration, passed, retries }) {
        console.log("===>" + test.parent);
        console.log("===>" + test.title);
        const status = passed ? 'passed' : 'failed';

        // Try multiple approaches to set Device Farm session metadata
        try {
            const sessionId = driver.sessionId;
            if (sessionId) {
                console.log(`Test completed: ${test.title} - Status: ${status}`);

                // Approach 1: Try the original Device Farm commands (for AWS Device Farm compatibility)
                try {
                    await driver.executeScript('devicefarm: setSessionStatus', [{ status: status }]);
                    await driver.executeScript('devicefarm: setSessionName', [{ name: test.parent }]);
                    console.log('Device Farm session status updated via executeScript');
                } catch (scriptError) {
                    console.log('executeScript approach failed:', scriptError.message);

                    // Approach 2: Try setting session metadata via session update
                    try {
                        await driver.execute('mobile: updateSession', {
                            testName: test.title,
                            testSuite: test.parent,
                            testStatus: status
                        });
                        console.log('Session metadata updated via mobile: updateSession');
                    } catch (mobileError) {
                        console.log('mobile: updateSession approach failed:', mobileError.message);

                        // Approach 3: Log to console for Device Farm plugin to pick up
                        console.log(`DEVICE_FARM_SESSION_UPDATE: ${JSON.stringify({
                            sessionId: sessionId,
                            testName: test.title,
                            testSuite: test.parent,
                            status: status,
                            timestamp: new Date().toISOString()
                        })}`);
                    }
                }
            }
        } catch (error) {
            console.log('All Device Farm update approaches failed (this is normal for local runs):', error.message);
        }
    },

    /**
     * Hook that gets executed after the suite has ended
     * @param {Object} suite suite details
     */
    afterSuite: function (suite) {
        // Additional cleanup if needed
        console.log('Suite completed:', suite.title);
    },

    suites: {
        login: [
            './test/specs/login.ts',
        ]
    },
    /**
     * Hook that gets executed after the suite has ended
     * @param {Object} suite suite details
     */
    // afterSuite: function (suite) {
    // },
    /**
     * Runs after a WebdriverIO command gets executed
     * @param {String} commandName hook command name
     * @param {Array} args arguments that command would receive
     * @param {Number} result 0 - command success, 1 - command error
     * @param {Object} error error object if any
     */
    // afterCommand: function (commandName, args, result, error) {
    // },
    /**
     * Gets executed after all tests are done. You still have access to all global variables from
     * the test.
     * @param {Number} result 0 - test pass, 1 - test fail
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {Array.<String>} specs List of spec file paths that ran
     */
    // after: function (result, capabilities, specs) {
    // },
    /**
     * Gets executed right after terminating the webdriver session.
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {Array.<String>} specs List of spec file paths that ran
     */
    // afterSession: function (config, capabilities, specs) {
    // },
    /**
     * Gets executed after all workers got shut down and the process is about to exit. An error
     * thrown in the onComplete hook will result in the test run failing.
     * @param {Object} exitCode 0 - success, 1 - fail
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {<Object>} results object containing test results
     */
    // onComplete: function(exitCode, config, capabilities, results) {
    // },
    /**
    * Gets executed when a refresh happens.
    * @param {String} oldSessionId session ID of the old session
    * @param {String} newSessionId session ID of the new session
    */
    //onReload: function(oldSessionId, newSessionId) {
    //}
}
