![Results](https://github.com/damarre/xero/blob/main/misc/result.png?raw=true)


# # APPIUM WebdriverIO

This project is an automated testing setup for a mobile application using WebdriverIO and Appium. It includes sample login tests and dashboard reset functionality.

## Prerequisites

Ensure you have the following installed on your system:
- Node.js (version 20.0.0 or higher)
- npm (comes with Node.js)
- Appium server

Install the necessary dependencies by running:

`npm install`

Run the test:

`npm run login`

Reset dashboard:

`npm run reset`

## Article

![See our journey](https://medium.com/@dear.ananta/exploring-appium-2-0-webdriverio-and-appium-device-farm-a-leap-forward-in-mobile-automation-e5f990c0e5a6)
