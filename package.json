{"name": "wdio_appium_js", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"login": "./node_modules/.bin/wdio wdio.conf.js --spec ./test/specs/login.ts", "reset": "appium plugin run device-farm reset"}, "keywords": [], "author": "", "license": "ISC", "jest": {"testEnvironment": "node"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "22.7.4", "@types/webdriverio": "^4.13.3", "@wdio/cli": "^9.1.2", "@wdio/config": "^9.1.2", "@wdio/local-runner": "^9.1.2", "@wdio/mocha-framework": "^9.1.2", "@wdio/spec-reporter": "^9.1.2", "@wdio/types": "^9.1.2", "ts-node": "^10.9.1", "typescript": "^5.2.2", "wdio-video-reporter": "^6.1.1"}, "dependencies": {"@wdio/appium-service": "^9.19.2", "webdriverio": "^9.19.2"}}